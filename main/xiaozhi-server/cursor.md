# cursor.md
这个文件为 cursor 提供指导，当它在这个代码库中工作时使用。

## 项目概述

这是一个基于 Python 的 WebSocket 和 HTTP 服务器应用程序，专为 ESP32 设备设计的 AI 助手服务器。它集成了多种 AI 模块，包括语音识别（ASR）、语音合成（TTS）、大语言模型（LLM）、意图识别（Intent）和记忆管理（Memory）。

## 代码架构

### 核心组件

1. **主应用 (app.py)**:
   - 程序入口点
   - 负责配置加载、日志设置和服务器启动
   - 同时启动 WebSocket 服务器和 HTTP 服务器

2. **WebSocket 服务器 (core/websocket_server.py)**:
   - 处理设备的 WebSocket 连接
   - 管理连接生命周期
   - 支持动态配置更新

3. **连接处理器 (core/connection.py)**:
   - 处理单个设备连接的所有逻辑
   - 管理 VAD、ASR、LLM、TTS 等模块的交互
   - 处理认证、对话历史、声纹识别等功能

4. **模块化架构**:
   - 采用工厂模式创建各种 AI 模块实例
   - 支持多种提供商实现（OpenAI、Gemini、Ollama 等）
   - 模块包括：VAD、ASR、LLM、TTS、Intent、Memory

5. **配置系统**:
   - 支持本地配置文件和远程 API 配置
   - 使用 YAML 格式配置文件
   - 支持配置热更新

### 数据流

1. 设备通过 WebSocket 连接到服务器
2. 服务器接收音频数据并进行 VAD（语音活动检测）
3. 有效的音频数据传递给 ASR 模块进行语音识别
4. 识别的文本传递给 LLM 模块生成回复
5. LLM 的回复通过 TTS 模块转换为音频
6. 音频通过 WebSocket 发送回设备

## 开发指南

### 常用命令

```bash
# 启动服务器
python app.py

# 运行性能测试
python performance_tester.py

# 使用 Docker 启动
docker-compose up

# 安装依赖
pip install -r requirements.txt
```

### 代码风格

- 遵循 Python 标准编码规范
- 使用类型注解
- 使用 loguru 进行日志记录

### 测试

- 使用 performance_tester.py 进行模块性能测试
- 通过 test 目录下的 HTML 文件进行 WebSocket 连接测试